<template>
  <Container>
    <header class="section-heading">
      <div class="heading-text">
        <h2 class="section-title">Most Search Recipes of the Week</h2>
        <p class="muted">
          Here are lists of popular and most search recipes of the week
        </p>
      </div>
      <RouterLink :to="{ name: 'Recipes' }" class="see-all-link"
        >see all</RouterLink
      >
    </header>
    <div class="recipe-grid">
      <div
        class="recipe-card"
        v-for="recipe in sliceRecipes"
        :key="recipe.id"
        @click="openModal(recipe)"
      >
        <img :src="recipe.image" :alt="recipe.title" loading="lazy" />
        <div class="recipe-card-overlay">
          <h3 class="recipe-card-title">{{ recipe.title }}</h3>
        </div>
      </div>
    </div>
    <!-- Recipe Section Modal  -->
    <RecipeModal
      :recipe="selectedRecipe"
      :showModal="isModalOpen"
      @close="closeModal"
    />
  </Container>
</template>
<script>
import { computed, ref } from "vue";
import Container from "./Container.vue";
import RecipeModal from "./RecipeModal.vue";
import useFetchRecipes from "@/composables/useFetchRecipes";
export default {
  name: "RecipeSection",
  components: {
    Container,
    RecipeModal,
  },
  setup() {
    const { recipes, fetchRecipes } = useFetchRecipes();
    fetchRecipes("breakfast");
    const sliceRecipes = computed(() => {
      return recipes.value.slice(0, 9);
    });

    const selectedRecipe = ref(null);
    const isModalOpen = ref(false);

    const openModal = (recipe) => {
      selectedRecipe.value = recipe;
      isModalOpen.value = true;
    };

    const closeModal = () => {
      isModalOpen.value = false;
    };

    return { sliceRecipes, openModal, closeModal, selectedRecipe, isModalOpen };
  },
};
</script>
<style scoped>
.section-heading {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.heading-text h2 {
  font-size: 1.6rem;
  text-transform: capitalize;
  color: var(--text-color);
  font-family: var(--font-family);
  font-weight: 500;
  margin-bottom: 0.5rem;
  transition: color var(--transition-medium);
}

.section-title {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-color);
  transition: color var(--transition-medium);
}

.muted {
  color: var(--text-muted);
  font-size: 0.9rem;
  transition: color var(--transition-medium);
}

.see-all-link {
  text-decoration: none;
  color: var(--primary-color);
  font-weight: 600;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  transition: all var(--transition-medium);
  border: 1px solid transparent;
}

.see-all-link:hover {
  color: var(--primary-color-light);
  background-color: var(--card-bg);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}
.recipe-card {
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.recipe-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}
.recipe-card {
  position: relative;
}

.recipe-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recipe-card .recipe-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.recipe-card:hover .recipe-card-overlay {
  opacity: 1;
}

.recipe-card .recipe-card-overlay .recipe-card-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: capitalize;
}

.recipe-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}
</style>
