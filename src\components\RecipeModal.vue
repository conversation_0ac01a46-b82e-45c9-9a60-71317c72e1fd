<!-- RecipeModal.vue -->
<template>
  <Transition name="modal-fade">
    <div class="modal-overlay" @click="closeModal" v-if="showModal && recipe">
      <div class="modal-content" @click.stop>
        <button class="close-button" @click="closeModal">&times;</button>
        <div style="display: flex; flex-direction: row">
          <img :src="recipe.image" :alt="recipe.title" class="modal-image" />
          <div style="margin-right: 20px">
            <h2 class="modal-title">{{ recipe.title }}</h2>
            <p class="modal-description">
              {{ recipe.description }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script>
export default {
  name: "RecipeModal",
  props: {
    recipe: {
      type: Object,
      required: false,
      default: null,
    },
    showModal: {
      type: Boolean,
      required: true,
    },
  },
  emits: ["close"],
  setup(_, { emit }) {
    const closeModal = () => {
      emit("close"); // Emit the 'close' event
    };
    return { closeModal };
  },
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-overlay);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--modal-bg);
  color: var(--modal-text);
  padding: 24px;
  border-radius: var(--border-radius);
  max-width: 600px;
  width: 90%;
  position: relative;
  box-shadow: var(--shadow-heavy);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
  max-height: 90vh;
  overflow-y: auto;
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 24px;
  cursor: pointer;
  border: none;
  background: none;
  color: var(--text-muted);
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.close-button:hover {
  background-color: var(--button-secondary-bg);
  color: var(--text-color);
  transform: scale(1.1);
}

.close-button:active {
  transform: scale(0.95);
}

.modal-image {
  max-width: 100%;
  height: auto;
  margin-bottom: 16px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
}

.modal-title {
  font-size: 1.2rem;
  margin-bottom: 12px;
  color: var(--primary-color);
  font-weight: 600;
  transition: color var(--transition-medium);
}

h2.modal-title {
  text-align: center;
}

.modal-description {
  font-size: 1rem;
  line-height: 1.6;
  color: var(--primary-color);
  transition: color var(--transition-medium);
}

/* Animation styles */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity var(--transition-medium) ease;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}

.modal-content {
  transition: transform var(--transition-medium) ease;
}

.modal-fade-enter-from .modal-content,
.modal-fade-leave-to .modal-content {
  transform: scale(0.8);
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    padding: 20px;
    margin: 20px;
  }

  .modal-title {
    font-size: 1.3rem;
  }

  .modal-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 98%;
    padding: 16px;
    margin: 10px;
  }

  .close-button {
    top: 8px;
    right: 8px;
    font-size: 20px;
    width: 28px;
    height: 28px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modal-fade-enter-active,
  .modal-fade-leave-active,
  .modal-content {
    transition: none;
  }

  .modal-fade-enter-from .modal-content,
  .modal-fade-leave-to .modal-content {
    transform: none;
  }
}
</style>
