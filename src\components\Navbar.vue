<template>
  <Container>
    <nav class="nav">
      <img class="logo" src="../assets/images/logo-orange.png" alt="logo" />
      <div class="nav-content">
        <div class="nav-links">
          <RouterLink to="/">Home</RouterLink>
          <RouterLink to="/recipes">Recipes</RouterLink>
          <RouterLink to="/add-recipe">Add Recipes</RouterLink>
          <RouterLink to="/articles">Articles</RouterLink>
        </div>
        <div class="user-icons">
          <ThemeToggle />
          <RouterLink :to="{ name: 'Auth' }">
            <User />
          </RouterLink>
        </div>
      </div>
    </nav>
  </Container>
</template>

<script>
import Container from "./Container.vue";
import User from "./icons/User.vue";
import ThemeToggle from "./ThemeToggle.vue";

export default {
  name: "Navbar",
  components: {
    User,
    Container,
    ThemeToggle,
  },
  data() {
    return {};
  },
};
</script>
<style scoped>
.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
}

.nav-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 40px;
}

.nav-links {
  display: flex;
  align-items: flex-end;
  gap: 20px;
}

.logo {
  width: 110px;
  display: block;
}

a {
  color: var(--primary-color);
  font-size: 14px;
  font-weight: 500;
}
a:hover {
  color: var(--primary-color-light);
}

a.router-link-exact-active:hover {
  color: var(--primary-color-light);
}

a.router-link-exact-active {
  color: var(--primary-color-light);
}
.user-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
