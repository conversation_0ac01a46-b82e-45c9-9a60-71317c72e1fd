<template>
  <footer class="footer">
    <Container>
      <div class="footer-section">
        <div class="footer-logo">
          <div class="footer-img">
            <img
              class="logo"
              src="../assets/images/logo-orange.png"
              alt="logo"
            />
          </div>
          <p class="footer-description muted">
            Discover 1000+ recipes in your hand with the best recipe book to
            find the easiest way to cook.
          </p>
        </div>
        <div class="footer-column-right">
          <div class="footer-newsletter">
            <h3 class="footer-subtitle">Sign up for our newsletter</h3>
            <form class="newsletter-form">
              <input
                type="email"
                class="newsletter-input"
                placeholder="Your email address"
              />
              <button
                class="newsletter-button"
                type="submit"
                @click.prevent="handleSubmit"
              >
                Submit
              </button>
            </form>
          </div>
          <div class="footer-links">
            <div class="footer-column">
              <h4 class="footer-heading">Menu</h4>
              <ul class="footer-list muted">
                <li><a href="#">Home</a></li>
                <li><a href="#">Recipes</a></li>
                <li><a href="#">Articles</a></li>
                <li><a href="#">About Us</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4 class="footer-heading">Help</h4>
              <ul class="footer-list">
                <li><a href="#">Privacy and Policy</a></li>
                <li><a href="#">Terms of Use</a></li>
              </ul>
            </div>
            <div class="footer-column">
              <h4 class="footer-heading">Social</h4>
              <ul class="footer-list">
                <li><a href="#">Facebook</a></li>
                <li><a href="#">Instagram</a></li>
                <li><a href="#">Twitter</a></li>
                <li><a href="#">Youtube</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </Container>
  </footer>
</template>

<script>
import Container from "@/components/Container.vue";
export default {
  name: "Footer",
  components: {
    Container,
  },
  setup() {
    return {};
  },
  methods: {
    handleSubmit() {
      console.log("submit");
    },
  },
};
</script>

<style scoped>
.footer {
  background-color: var(--footer-bg);
  color: var(--footer-text);
  padding: 20px 0;
  margin-top: 50px;
  transition: background-color var(--transition-medium),
    color var(--transition-medium);
}

.footer-links {
  display: flex;
  gap: 70px;
}

.footer-list li {
  margin-bottom: 10px;
}

.footer-list li a {
  font-size: 13px;
  text-decoration: none;
  color: var(--footer-text);
  transition: color var(--transition-medium);
}

.footer-section {
  margin: 30px 0;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.footer-newsletter {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.footer-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.footer-icon {
  font-size: 28px;
  margin-right: 10px;
}

.footer-description {
  font-size: 14px;
  line-height: 1.5;
  width: 45%;
  margin-top: 10px;
}

.footer-subtitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
}

.newsletter-form {
  display: flex;
}

.newsletter-input {
  border: 1px solid #6b707e; /* Gray border */
  border-radius: 5px 0 0 5px;
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
}

.newsletter-button {
  background-color: var(--primary-color); /* Teal */
  color: #fff;
  border: none;
  border-radius: 0 5px 5px 0;
  padding: 10px 15px;
  cursor: pointer;
}

.newsletter-button:hover {
  background-color: var(--primary-color-light); /* Darker teal on hover */
}

.footer-column {
  width: 100%;
}

.footer-heading {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.footer-list a:hover {
  color: #6e6c6c; /* White on hover */
}

@media (min-width: 768px) {
  .footer-section {
    margin-bottom: 0;
  }

  .footer-section:not(:first-child) {
    margin-left: 30px;
  }

  .footer-column {
    width: auto;
  }
}
</style>
